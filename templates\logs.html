{% extends "base.html" %}

{% block title %}Logs - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link active" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-file-alt text-info me-2"></i>Bot Activity Logs</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4 class="text-primary">{{ stats.total_logs }}</h4>
                        <small class="text-muted">Total Logs ({{ stats.period_days }} days)</small>
                    </div>
                </div>
            </div>
            {% for category_stat in stats.category_stats[:3] %}
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4 class="text-success">{{ category_stat.count }}</h4>
                        <small class="text-muted">{{ category_stat._id.title() }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">All Categories</option>
                            <option value="repping" {% if current_category == 'repping' %}selected{% endif %}>Repping System</option>
                            <option value="vent" {% if current_category == 'vent' %}selected{% endif %}>Vent System</option>
                            <option value="tempvoice" {% if current_category == 'tempvoice' %}selected{% endif %}>Temp Voice</option>
                            <option value="sticky" {% if current_category == 'sticky' %}selected{% endif %}>Sticky Messages</option>
                            <option value="dm_support" {% if current_category == 'dm_support' %}selected{% endif %}>DM Support</option>
                            <option value="gender_verification" {% if current_category == 'gender_verification' %}selected{% endif %}>Gender Verification</option>
                            <option value="config" {% if current_category == 'config' %}selected{% endif %}>Configuration</option>
                            <option value="general" {% if current_category == 'general' %}selected{% endif %}>General</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="limit" class="form-label">Limit</label>
                        <select class="form-select" id="limit" name="limit">
                            <option value="50" {% if current_limit == 50 %}selected{% endif %}>50 logs</option>
                            <option value="100" {% if current_limit == 100 %}selected{% endif %}>100 logs</option>
                            <option value="250" {% if current_limit == 250 %}selected{% endif %}>250 logs</option>
                            <option value="500" {% if current_limit == 500 %}selected{% endif %}>500 logs</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <button type="button" class="btn btn-success me-2" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button type="button" class="btn btn-warning" onclick="cleanupLogs()">
                            <i class="fas fa-broom me-1"></i>Cleanup
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Activity Logs</h6>
                <span class="badge bg-info">{{ logs|length }} logs shown</span>
            </div>
            <div class="card-body p-0">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-dark table-striped mb-0">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Category</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody id="logs-tbody">
                            {% for log in logs %}
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        {{ log.timestamp.strftime('%m/%d %H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    {% set category_colors = {
                                        'repping': 'warning',
                                        'vent': 'danger',
                                        'tempvoice': 'info',
                                        'sticky': 'success',
                                        'dm_support': 'primary',
                                        'gender_verification': 'secondary',
                                        'config': 'dark',
                                        'general': 'light'
                                    } %}
                                    <span class="badge bg-{{ category_colors.get(log.category, 'secondary') }}">
                                        {{ log.category.title() }}
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        {{ log.username }}<br>
                                        <span class="text-muted">{{ log.user_id }}</span>
                                    </small>
                                </td>
                                <td>{{ log.action }}</td>
                                <td>
                                    {% if log.details %}
                                    <small class="text-muted">{{ log.details[:100] }}{% if log.details|length > 100 %}...{% endif %}</small>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No logs found</h5>
                    <p class="text-muted">No activity logs match your current filters.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

function refreshLogs() {
    // Get current filter values
    const category = document.getElementById('category').value;
    const limit = document.getElementById('limit').value;

    // Build query string
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (limit) params.append('limit', limit);

    // Reload page with filters
    window.location.href = '{{ url_for("logs") }}?' + params.toString();
}

function cleanupLogs() {
    if (!confirm('This will delete old log entries, keeping only the most recent 100 logs. Continue?')) {
        return;
    }

    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Cleaning...';
    button.disabled = true;

    fetch('/api/cleanup-logs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Successfully cleaned up ${data.deleted_count} old log entries.`);
            refreshLogs();
        } else {
            alert('Error cleaning up logs: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error cleaning up logs. Please try again.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if we're still on the logs page
    if (window.location.pathname === '{{ url_for("logs") }}') {
        refreshLogs();
    }
}, 30000);
</script>
{% endblock %}
