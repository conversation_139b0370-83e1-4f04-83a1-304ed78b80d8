{% extends "base.html" %}

{% block title %}Configure Automod - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('Sticky Messages')">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('DM Support')">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('Gender Verification')">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link active" href="{{ url_for('configure_automod') }}">
                    <i class="fas fa-robot"></i>Automod
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-robot text-secondary me-2"></i>Automod Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Add Ignored User -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Manage Ignored Users</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="add">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="user_id" class="form-label">User ID</label>
                                    <input type="text" class="form-control" id="user_id" name="user_id" 
                                           placeholder="e.g., 123456789012345678" required>
                                    <div class="form-text">
                                        Discord user ID to ignore from repping system
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-plus me-2"></i>Add User
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Current Ignored Users -->
                        {% if ignored_users %}
                        <h6 class="mb-3">Currently Ignored Users ({{ ignored_users|length }})</h6>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>User ID</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user_id in ignored_users %}
                                    <tr>
                                        <td><code>{{ user_id }}</code></td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="remove">
                                                <input type="hidden" name="user_id" value="{{ user_id }}">
                                                <button type="submit" class="btn btn-danger btn-sm" 
                                                        onclick="return confirm('Are you sure you want to remove this user from the ignored list?')">
                                                    <i class="fas fa-trash me-1"></i>Remove
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No users are currently being ignored by the repping system.
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Log Channel Configuration -->
                {% if config and config.log_channel_id %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Log Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Status:</strong>
                                <br><span class="badge bg-success">Configured</span>
                            </div>
                        </div>
                        <hr>
                        <p class="text-muted mb-0">
                            Detailed role assignment and removal logs are sent to this channel.
                        </p>
                    </div>
                </div>
                {% else %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Log Channel Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No log channel configured. Configure the repping system to set up detailed logging.
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get User ID</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a user</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>What Are Ignored Users?</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Ignored users are:</p>
                        <ul class="mb-3">
                            <li>Excluded from the repping system</li>
                            <li>Won't get roles automatically assigned</li>
                            <li>Won't have roles removed</li>
                            <li>Useful for bots and special accounts</li>
                        </ul>
                        <p class="text-info mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> All bots are automatically ignored by the system.
                        </p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Common Use Cases</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Add to ignored list:</p>
                        <ul class="mb-0">
                            <li>Music bots</li>
                            <li>Moderation bots</li>
                            <li>Staff members who shouldn't be affected</li>
                            <li>Special role accounts</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary">{{ ignored_users|length }}</h4>
                                <small class="text-muted">Ignored Users</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">{{ server_info.member_count - ignored_users|length }}</h4>
                                <small class="text-muted">Monitored Users</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showComingSoon(feature) {
    alert(`${feature} configuration will be available in the web dashboard soon! For now, please use the Discord slash commands.`);
}
</script>
{% endblock %}
